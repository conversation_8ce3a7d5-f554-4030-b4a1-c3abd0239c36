package consumer

import (
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/cockroachdb/errors"

	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/topology"
)

// SharedQueueConsumerGroup consumes messages from a shared queue
type SharedQueueConsumerGroup struct {
	*BaseQueueConsumerGroup
	exchangeName  string     // Main exchange name
	baseQueueName string     // Base name for the shared queue (a suffix will be added to real queue names)
	topics        []string   // Topics to consume from
	workerCount   int        // Number of workers for the queue
	mutex         sync.Mutex // Additional mutex for shared-specific operations
}

// NewSharedQueueConsumerGroup creates a consumer for a shared queue
func NewSharedQueueConsumerGroup(
	connection *rabbitmq.Connection,
	exchangeName string,
	baseQueueName string,
	topics []string,
	handler queue.MessageHandler,
	workerCount int,
	maxRetries int,
	processingTimeout time.Duration,
) (queue.ConsumerGroup, error) {
	if workerCount < 1 {
		return nil, errors.New("worker count must be at least 1")
	}

	logger := slog.With(
		"component", "shared-queue-consumer",
		"baseQueueName", baseQueueName,
		"workers", workerCount,
	)

	top := topology.NewSharedQueueTopology(exchangeName, baseQueueName, topics)

	return &SharedQueueConsumerGroup{
		BaseQueueConsumerGroup: NewBaseQueueConsumerGroup(connection, handler, maxRetries, top, processingTimeout, logger),
		exchangeName:           exchangeName,
		baseQueueName:          baseQueueName,
		topics:                 topics,
		workerCount:            workerCount,
	}, nil
}

// Start begins consuming from the queue
func (c *SharedQueueConsumerGroup) Start() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isRunning {
		c.logger.Debug("Consumer is already running")
		return nil
	}

	// Setup topology
	if err := c.setupTopology(); err != nil {
		return errors.Wrap(err, "failed to set up retry for shared queue consumer")
	}

	// Setup publishing channel pool for retries and DLQ
	if err := c.setupPubChannelPool(); err != nil {
		return errors.Wrap(err, "failed to set up pub channel pool for partitioned queue consumer")
	}

	c.logger.Info("Starting consumption", "workers", c.workerCount)

	// Create and start all workers
	for i := 0; i < c.workerCount; i++ {
		// Create consumer name
		name := fmt.Sprintf("%s-consumer-%d", c.baseQueueName, i)
		c.logger.Debug("Creating consumer", "name", name, "index", i)

		// Create the consumer with simplified parameters
		consumer, err := NewConsumer(
			c.connection,
			c.pubChannelPool,
			name,
			c.topology.GetQueueName(), // Queue name from topology
			c.topology,                // Pass the topology
			c.handler,
			1,
			c.retryPolicy,
			c.processingTimeout,
		)
		if err != nil {
			// If we failed to create this consumer, stop all previous ones
			c.logger.Error("Failed to create consumer", "index", i, "error", err)
			c.StopAllConsumers()
			return errors.Wrapf(err, "failed to create consumer %s", name)
		}

		// Start the consumer
		c.logger.Debug("Starting consumer", "name", name)
		if err := consumer.Start(); err != nil {
			// If we failed to start this consumer, stop all previous ones
			c.logger.Error("Failed to start consumer", "index", i, "error", err)
			if err := consumer.Stop(); err != nil { // Try to clean up this one
				c.logger.Error("Error stopping consumer", "error", err)
			}
			c.StopAllConsumers()
			return errors.Wrapf(err, "failed to start consumer %s", name)
		}

		c.consumers = append(c.consumers, consumer)
		c.logger.Debug("Consumer started successfully", "name", name)
	}

	// Start the retry queue cleanup routine
	//c.startRetryQueueCleanup()

	c.isRunning = true
	c.logger.Info("Started all consumers successfully", "activeCount", len(c.consumers))
	return nil
}
