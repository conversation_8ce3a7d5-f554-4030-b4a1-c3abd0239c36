package topology

import (
	"fmt"
	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"
	"log/slog"
	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"
)

// BaseTopology provides common functionality for all topology types
type BaseTopology struct {
	// Basic attributes
	exchangeName          string
	queuesBaseName        string
	workQueueName         string
	dlqName               string
	topics                []string
	retryDLRoutingKey     string // retry dead letter routing key (where do the messages go after the retry TTL expires)
	multipleRetriesQueues bool

	// Logger
	logger *slog.Logger
}

// NewBaseTopology creates a base topology with common fields
func NewBaseTopology(exchangeName string, queuesBaseName string, topics []string, retryDLRoutingKey string) *BaseTopology {
	workQueueName := fmt.Sprintf("%s.work", queuesBaseName)
	dlqName := fmt.Sprintf("%s.dlq", queuesBaseName)

	if retryDLRoutingKey == "" {
		retryDLRoutingKey = workQueueName
	}

	return &BaseTopology{
		exchangeName:          exchangeName,
		queuesBaseName:        queuesBaseName,
		workQueueName:         workQueueName,
		dlqName:               dlqName,
		topics:                topics,
		retryDLRoutingKey:     retryDLRoutingKey,
		multipleRetriesQueues: false,
		logger:                packageLogger.With("topology", "base"),
	}
}

func (b *BaseTopology) GetExchangeName() string {
	return b.exchangeName
}

func (b *BaseTopology) GetQueueName() string {
	return b.workQueueName
}

func (b *BaseTopology) GetDLQName() string {
	return b.dlqName
}

func (b *BaseTopology) GetTopics() []string {
	return b.topics
}

func (b *BaseTopology) GetPartitionQueueNames() []string {
	return nil
}

func (b *BaseTopology) GetRouterExchangeName() string {
	return ""
}

// EnableMultipleRetryQueues enables the multiple retry queues mode
// This creates separate queues for each TTL value instead of using a single retry queue
func (b *BaseTopology) EnableMultipleRetryQueues() {
	b.multipleRetriesQueues = true
}

// Setup is implemented by derived topologies
func (b *BaseTopology) Setup(connection *rabbitmq.Connection) error {
	return errors.New("Setup must be implemented by derived topology")
}

// SetupDLQ sets up the dead letter queue for any topology
func (b *BaseTopology) SetupDLQ(connection *rabbitmq.Connection) error {
	dlqLogger := b.logger.With(
		"operation", "SetupDLQ",
		"dlq", b.dlqName,
	)

	// Create a channel for DLQ setup
	channel, err := connection.Channel()
	if err != nil {
		dlqLogger.Error("Failed to create channel for DLQ setup", "error", err)
		return errors.Wrap(err, "failed to create channel for DLQ setup")
	}
	defer channel.Close()

	// Declare the dead letter queue (no DLX, final destination)
	dlqLogger.Debug("Declaring DLQ queue")
	if _, err := channel.QueueDeclare(
		b.dlqName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		amqp.Table{
			"x-queue-type": "quorum",
		},
	); err != nil {
		dlqLogger.Error("Failed to declare DLQ", "error", err)
		return errors.Wrapf(err, "failed to declare DLQ '%s'", b.dlqName)
	}

	if err := channel.QueueBind(
		b.dlqName,      // queue name
		b.dlqName,      // routing key
		b.exchangeName, // exchange
		false,          // no-wait
		nil,            // arguments
	); err != nil {
		dlqLogger.Error("Failed to bind DLQ", "error", err)
		return errors.Wrapf(err, "failed to bind DLQ '%s' to exchange '%s'",
			b.dlqName, b.exchangeName)
	}

	dlqLogger.Info("Successfully set up DLQ queue")
	return nil
}

func (b *BaseTopology) GetRetryQueueName(ttlMs int64) string {
	if b.multipleRetriesQueues {
		return b.getRetryQueueNameMultiple(ttlMs)
	} else {
		return b.getRetryQueueNameSingle()
	}
}

func (b *BaseTopology) SetupRetryQueues(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error {
	if b.multipleRetriesQueues {
		return b.setupRetryQueuesMultiple(connection, retryPolicy)
	} else {
		return b.setupRetryQueuesSingle(connection, retryPolicy)
	}
}

// SetupRetryQueues creates all retry queues based on the retry policy
func (b *BaseTopology) setupRetryQueuesMultiple(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error {
	retryLogger := b.logger.With(
		"operation", "SetupRetryQueues",
		"maxRetries", retryPolicy.GetMaxRetries(),
	)

	retryLogger.Info("Setting up retry queues")

	// Create a channel for retry queues setup
	channel, err := connection.Channel()
	if err != nil {
		retryLogger.Error("Failed to create channel for retry queues setup", "error", err)
		return errors.Wrap(err, "failed to create channel for retry queues setup")
	}
	defer channel.Close()

	// Create a retry queue for each backoff interval
	var allTTLs []int64
	for i := 0; i < retryPolicy.GetMaxRetries(); i++ {
		ttlMs := retryPolicy.NextTTL(i)
		allTTLs = append(allTTLs, ttlMs)
	}

	// Create each unique retry queue
	seen := make(map[int64]bool)
	for _, ttlMs := range allTTLs {
		if seen[ttlMs] {
			continue // Skip if we've already created this TTL queue
		}
		seen[ttlMs] = true

		retryQueueName := b.GetRetryQueueName(ttlMs)
		queueLogger := retryLogger.With(
			"retryQueue", retryQueueName,
			"ttlMs", ttlMs,
		)

		queueLogger.Debug("Creating retry queue")

		// Declare the retry queue with the specific TTL
		args := amqp.Table{
			"x-queue-type":              "quorum",
			"x-dead-letter-exchange":    b.exchangeName,
			"x-dead-letter-routing-key": b.retryDLRoutingKey,
			"x-message-ttl":             ttlMs,
		}

		_, err := channel.QueueDeclare(
			retryQueueName, // Name
			true,           // Durable
			false,          // Auto-delete
			false,          // Exclusive
			false,          // No-wait
			args,           // Arguments
		)

		if err != nil {
			queueLogger.Error("Failed to declare retry queue", "error", err)
			return errors.Wrapf(err, "failed to declare retry queue '%s'", retryQueueName)
		}

		queueLogger.Info("Created retry queue")
	}

	retryLogger.Info("Successfully set up all retry queues")
	return nil
}

func (b *BaseTopology) getRetryQueueNameMultiple(ttlMs int64) string {
	if ttlMs < 1000 {
		return fmt.Sprintf("%s.retry.%dms", b.queuesBaseName, ttlMs)
	} else if ttlMs < 60000 {
		seconds := float64(ttlMs) / 1000
		return fmt.Sprintf("%s.retry.%.1fs", b.queuesBaseName, seconds)
	} else if ttlMs < 3600000 {
		minutes := float64(ttlMs) / 60000
		return fmt.Sprintf("%s.retry.%.1fm", b.queuesBaseName, minutes)
	} else {
		hours := float64(ttlMs) / 3600000
		return fmt.Sprintf("%s.retry.%.1fh", b.queuesBaseName, hours)
	}
}

// GetRetryQueueName returns the name of the single retry queue
func (b *BaseTopology) getRetryQueueNameSingle() string {
	// Now we simply return the single retry queue name
	// The ttlMs parameter is kept for backward compatibility
	return fmt.Sprintf("%s.retry", b.queuesBaseName)
}

// SetupRetryQueues creates a single retry queue
func (b *BaseTopology) setupRetryQueuesSingle(connection *rabbitmq.Connection, retryPolicy queue.RetryPolicy) error {
	retryLogger := b.logger.With(
		"operation", "SetupRetryQueues",
		"maxRetries", retryPolicy.GetMaxRetries(),
	)

	retryLogger.Info("Setting up single retry queue")

	// Create a channel for retry queue setup
	channel, err := connection.Channel()
	if err != nil {
		retryLogger.Error("Failed to create channel for retry queue setup", "error", err)
		return errors.Wrap(err, "failed to create channel for retry queue setup")
	}
	defer channel.Close()

	// Create a single retry queue that doesn't have a fixed TTL
	// TTL will be set per message instead
	retryQueueName := b.getRetryQueueNameSingle()
	queueLogger := retryLogger.With(
		"retryQueue", retryQueueName,
	)

	queueLogger.Debug("Creating retry queue")

	// Declare the retry queue without a fixed TTL
	args := amqp.Table{
		"x-queue-type":              "quorum",
		"x-dead-letter-exchange":    b.exchangeName,
		"x-dead-letter-routing-key": b.retryDLRoutingKey,
	}

	_, err = channel.QueueDeclare(
		retryQueueName, // Name
		true,           // Durable
		false,          // Auto-delete
		false,          // Exclusive
		false,          // No-wait
		args,           // Arguments
	)

	if err != nil {
		queueLogger.Error("Failed to declare retry queue", "error", err)
		return errors.Wrapf(err, "failed to declare retry queue '%s'", retryQueueName)
	}

	queueLogger.Info("Created retry queue")
	retryLogger.Info("Successfully set up single retry queue")
	return nil
}
