package topology

import (
	"fmt"

	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"

	"queue-manager/common/rabbitmq"
)

type PartitionedQueueTopology struct {
	*BaseTopology
	partitionCount     int
	partitionNames     []string
	routerExchangeName string
}

func NewPartitionedQueueDefinition(mainExchange string, queuesBaseName string, partitionCount int, topics []string) Topology {
	// Generate the partition queue names
	partitionNames := make([]string, partitionCount)
	for i := 0; i < partitionCount; i++ {
		partitionNames[i] = fmt.Sprintf("%s.work.%d", queuesBaseName, i+1)
	}

	routerExchangeName := fmt.Sprintf("%s_router_exchange", queuesBaseName)

	return &PartitionedQueueTopology{
		BaseTopology: NewBaseTopology(
			mainExchange,       // main exchange name
			queuesBaseName,     // base name for retry queues (will append index)
			topics,             // topics to bind to
			routerExchangeName, // retry dead letter routing key (where do the messages go after the retry TTL expires)
		),
		partitionCount:     partitionCount,
		partitionNames:     partitionNames,
		routerExchangeName: routerExchangeName,
	}
}

func (q *PartitionedQueueTopology) GetPartitionQueueNames() []string {
	return q.partitionNames
}

func (q *PartitionedQueueTopology) GetRouterExchangeName() string {
	return q.routerExchangeName
}

// Setup sets up exchanges and queues for partitioned queues
func (q *PartitionedQueueTopology) Setup(connection *rabbitmq.Connection) error {
	topologyLogger := packageLogger.With(
		"operation", "SetupPartitionedQueueTopology",
		"baseExchange", q.exchangeName,
		"partitionCount", len(q.partitionNames),
	)

	topologyLogger.Debug("Setting up partitioned queue topology")

	// Create our own channel for this topology setup
	channel, err := connection.Channel()
	if err != nil {
		topologyLogger.Error("Failed to create channel for topology setup", "error", err)
		return errors.Wrap(err, "failed to create channel for partitioned queue topology setup")
	}
	defer channel.Close()

	// Declare Consistent Hash Router Exchange
	topologyLogger.Debug("Declaring Router exchange", "exchange", q.routerExchangeName)
	if err := channel.ExchangeDeclare(
		q.routerExchangeName, // name
		"x-consistent-hash",  // consistent hash type
		true,                 // durable
		false,                // auto-deleted
		false,                // internal
		false,                // no-wait
		amqp.Table{
			"hash-header": rabbitmq.ConsistentHashHeader, // specify the header to use for hashing
			"x-hash-on":   rabbitmq.ConsistentHashHeader, // for LavinMQ
		},
	); err != nil {
		topologyLogger.Error("Failed to declare Router exchange", "error", err)
		return errors.Wrapf(err, "failed to declare Router exchange '%s'", q.routerExchangeName)
	}

	// Bind Main Exchange to the Router Exchange by name, it will be used to route retries back from the main to the router
	if err := channel.ExchangeBind(
		q.routerExchangeName, // queue name
		q.routerExchangeName, // routing key
		q.exchangeName,       // exchange
		false,                // no-wait
		nil,                  // arguments
	); err != nil {
		topologyLogger.Error("Failed to bind topic to partition",
			"routerExchange", q.routerExchangeName,
			"topic", q.routerExchangeName,
			"error", err)
		return errors.Wrapf(err, "failed to bind topic '%s' to main exchange on router exchange '%s'",
			q.routerExchangeName, q.routerExchangeName)
	}

	// Setup DLQ using the base implementation
	if err := q.SetupDLQ(connection); err != nil {
		return err
	}

	// Add topic bindings if provided
	for _, topic := range q.topics {
		topologyLogger.Debug("Binding router exchange to main exchange",
			"routerExchange", q.routerExchangeName,
			"topic", topic)

		if err := channel.ExchangeBind(
			q.routerExchangeName, // queue name
			topic,                // routing key
			q.exchangeName,       // exchange
			false,                // no-wait
			nil,                  // arguments
		); err != nil {
			topologyLogger.Error("Failed to bind topic to partition",
				"routerExchange", q.routerExchangeName,
				"topic", topic,
				"error", err)
			return errors.Wrapf(err, "failed to bind topic '%s' to main exchange on router exchange '%s'",
				topic, q.routerExchangeName)
		}
	}

	// Declare each partition queue and bind to the exchange
	for _, partitionName := range q.partitionNames {
		topologyLogger.Debug("Setting up partition queue", "partitionQueue", partitionName)

		if _, err := channel.QueueDeclare(
			partitionName, // name
			true,          // durable
			false,         // delete when unused
			false,         // exclusive
			false,         // no-wait
			amqp.Table{
				"x-queue-type":              "quorum",
				"x-single-active-consumer":  true,
				"x-delivery-limit":          10,
				"x-dead-letter-exchange":    q.exchangeName,
				"x-dead-letter-routing-key": q.dlqName,
			}, // arguments
		); err != nil {
			return errors.Wrapf(err, "failed to declare partition queue '%s'", partitionName)
		}

		// Bind the partition queue to the exchange using the queue name as routing key
		if err := channel.QueueBind(
			partitionName,        // queue name
			"500",                // routing key
			q.routerExchangeName, // exchange
			false,                // no-wait
			nil,                  // arguments
		); err != nil {
			return errors.Wrapf(err, "failed to bind partition queue '%s'", partitionName)
		}
	}

	// Handle partition decrease if needed
	handler := NewPartitionDecreaseHandler(q.queuesBaseName, q.routerExchangeName)
	if err := handler.HandlePartitionDecrease(connection, len(q.partitionNames), topologyLogger); err != nil {
		return err
	}

	topologyLogger.Info("Successfully set up partitioned queue topology",
		"partitionCount", len(q.partitionNames),
		"dlqQueue", q.dlqName,
		"topicCount", len(q.topics))

	return nil
}