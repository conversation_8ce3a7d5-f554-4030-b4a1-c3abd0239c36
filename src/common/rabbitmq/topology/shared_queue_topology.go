package topology

import (
	"github.com/cockroachdb/errors"
	amqp "github.com/rabbitmq/amqp091-go"
	"queue-manager/common/rabbitmq"
)

type SharedQueueTopology struct {
	*BaseTopology
}

// NewSharedQueueTopology creates a shared queue topology with the given base name
func NewSharedQueueTopology(mainExchange string, queuesBaseName string, topics []string) Topology {
	return &SharedQueueTopology{
		BaseTopology: NewBaseTopology(
			mainExchange,   // exchange name
			queuesBaseName, // work queue name
			topics,         // topics to bind to
			"",             // retry dead letter routing key (where do the messages go after the retry TTL expires) (defaults to work queue)
		),
	}
}

// Setup sets up exchanges, queues, and bindings for a shared queue
func (q *SharedQueueTopology) Setup(connection *rabbitmq.Connection) error {
	topologyLogger := packageLogger.With(
		"operation", "SetupSharedQueueTopology",
		"queue", q.workQueueName,
		"exchange", q.exchangeName,
	)

	topologyLogger.Debug("Setting up shared queue topology")

	// Create our own channel for this topology setup
	channel, err := connection.Channel()
	if err != nil {
		topologyLogger.Error("Failed to create channel for topology setup", "error", err)
		return errors.Wrap(err, "failed to create channel for shared queue topology setup")
	}
	defer channel.Close()

	// Declare the working queue
	topologyLogger.Debug("Declaring working queue", "queue", q.workQueueName)
	if _, err := channel.QueueDeclare(
		q.workQueueName, // name
		true,            // durable
		false,           // delete when unused
		false,           // exclusive
		false,           // no-wait
		amqp.Table{
			"x-queue-type":              "quorum",
			"x-delivery-limit":          10,
			"x-dead-letter-exchange":    q.exchangeName,
			"x-dead-letter-routing-key": q.dlqName,
		},
	); err != nil {
		topologyLogger.Error("Failed to declare working queue", "error", err)
		return errors.Wrapf(err, "failed to declare working queue '%s'", q.queuesBaseName)
	}

	// Setup DLQ using the base implementation
	if err := q.SetupDLQ(connection); err != nil {
		return err
	}

	// Bind the working queue to the working exchange
	topologyLogger.Debug("Binding working queue to exchange",
		"queue", q.workQueueName,
		"exchange", q.exchangeName)

	if err := channel.QueueBind(
		q.workQueueName, // queue name
		q.workQueueName, // routing key
		q.exchangeName,  // exchange
		false,           // no-wait
		nil,             // arguments
	); err != nil {
		topologyLogger.Error("Failed to bind working queue", "error", err)
		return errors.Wrapf(err, "failed to bind working queue '%s' to exchange '%s'",
			q.workQueueName, q.exchangeName)
	}

	// Set up topic bindings if provided
	for _, topic := range q.topics {
		topologyLogger.Debug("Binding topic to queue",
			"queue", q.workQueueName,
			"topic", topic)

		if err := channel.QueueBind(
			q.workQueueName, // queue name
			topic,           // routing key
			q.exchangeName,  // exchange
			false,           // no-wait
			nil,             // arguments
		); err != nil {
			topologyLogger.Error("Failed to bind topic", "topic", topic, "error", err)
			return errors.Wrapf(err, "failed to bind topic '%s' to queue '%s'",
				topic, q.workQueueName)
		}
	}

	topologyLogger.Info("Successfully set up shared queue topology",
		"mainQueue", q.workQueueName,
		"dlqQueue", q.dlqName,
		"topicCount", len(q.topics))

	return nil
}
