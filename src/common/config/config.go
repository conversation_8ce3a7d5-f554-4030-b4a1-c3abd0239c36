package config

import (
	"flag"
	"log/slog"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"

	"github.com/joho/godotenv"
)

func getDotEnvPath() string {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		panic("Can not get path to dotenv file")
	}
	dirname := filepath.Dir(filename)

	file := "../../.env"

	if flag.Lookup("test.v") != nil {
		file = "../../.env.test"
	}

	return path.Join(dirname, file)
}

func Load() {
	err := godotenv.Load(getDotEnvPath())
	if err != nil {
		slog.Warn("Error loading .env file", slog.Any("error", err))
	}
}

func GetLogLevel() slog.Level {
	switch strings.ToUpper(os.Getenv("LOG_LEVEL")) {
	case "DEBUG":
		return slog.LevelDebug
	case "INFO":
		return slog.LevelInfo
	case "WARN":
		return slog.LevelWarn
	case "ERROR":
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

func GetLogType() string {
	return os.Getenv("LOG_TYPE")
}

func GetRabbitMQHost() string {
	host := os.Getenv("RABBITMQ_HOST")
	if host == "" {
		return "localhost"
	}
	return host
}

func GetRabbitMQPort() string {
	port := os.Getenv("RABBITMQ_PORT")
	if port == "" {
		return "5672"
	}
	return port
}

func GetRabbitMQUsername() string {
	username := os.Getenv("RABBITMQ_USER")
	if username == "" {
		return "guest"
	}
	return username
}

func GetRabbitMQPassword() string {
	password := os.Getenv("RABBITMQ_PASS")
	if password == "" {
		return "guest"
	}
	return password
}

func GetRabbitMQVHost() string {
	vhost := os.Getenv("RABBITMQ_VHOST")
	if vhost == "" {
		return "/"
	}
	return vhost
}

func GetConsumerStartupSleepEnabled() bool {
	enabled := os.Getenv("CONSUMER_STARTUP_SLEEP_ENABLED")
	if enabled == "" {
		return false // Default to disabled for backward compatibility
	}

	parsed, err := strconv.ParseBool(enabled)
	if err != nil {
		// Log warning and return default if parsing fails
		slog.Warn("Invalid value for CONSUMER_STARTUP_SLEEP_ENABLED, using default (false)",
			"value", enabled,
			"error", err)
		return false
	}

	return parsed
}
