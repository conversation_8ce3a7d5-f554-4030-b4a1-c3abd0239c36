package service

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	json "github.com/bytedance/sonic"

	"queue-manager/common/default_connection"
	"queue-manager/common/queue"
	"queue-manager/common/queue_config"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/consumer"
	"queue-manager/consumer/worker"
)

// JobProcessor handles the processing of jobs consumed from queues
type JobProcessor struct {
	connection                 *rabbitmq.Connection
	config                     *queue_config.Config
	httpWorker                 *worker.HTTPWorker
	sharedConsumersGroups      map[string]queue.ConsumerGroup
	partitionedConsumersGroups map[string]queue.ConsumerGroup
	mutex                      sync.RWMutex
	logger                     *slog.Logger
}

// NewJobProcessor creates a new job processor
func NewJobProcessor(connection *rabbitmq.Connection, cfg *queue_config.Config, httpWorker *worker.HTTPWorker) *JobProcessor {
	logger := slog.With("component", "job-processor")

	return &JobProcessor{
		connection:                 connection,
		config:                     cfg,
		httpWorker:                 httpWorker,
		sharedConsumersGroups:      make(map[string]queue.ConsumerGroup),
		partitionedConsumersGroups: make(map[string]queue.ConsumerGroup),
		logger:                     logger,
	}
}

// StartProcessing starts consumers for all queues defined in the configuration
func (p *JobProcessor) StartProcessing() error {
	p.logger.Info("Starting job processing for all queues")

	// Process each queue configuration
	for queueName, queueCfg := range p.config.Queues {
		// Check if this is a sequential queue
		if queueCfg.Mode == "sequential" {
			if err := p.startPartitionedQueueConsumer(queueName, queueCfg); err != nil {
				return fmt.Errorf("failed to start consumer for partitioned queue '%s': %w", queueName, err)
			}
		} else {
			if err := p.startSharedQueueConsumer(queueName, queueCfg); err != nil {
				return fmt.Errorf("failed to start consumer for shared queue '%s': %w", queueName, err)
			}
		}
	}

	p.logger.Info("All queue consumers started")
	return nil
}

// startPartitionedQueueConsumer starts a consumer for a partitioned queue
func (p *JobProcessor) startPartitionedQueueConsumer(queueName string, queueCfg *queue_config.Queue) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Check if we already have a consumerGroup for this queue
	if _, exists := p.partitionedConsumersGroups[queueName]; exists {
		p.logger.Debug("Consumer already exists for partitioned queue", "queue", queueName)
		return nil
	}

	p.logger.Info("Creating partitioned queue consumerGroup")

	// Create the message handler
	handler := p.createMessageHandler(queueName)

	println("queueCfg.ProcessingTimeoutSeconds", queueCfg.ProcessingTimeoutSeconds)

	// Create the consumerGroup
	consumerGroup, err := consumer.NewPartitionedQueueConsumerGroup(
		p.connection,
		default_connection.MainExchangeName,
		queueName,
		queueCfg.Topics,
		queueCfg.Partitions,
		handler,
		queueCfg.MaxRetries,
		time.Duration(queueCfg.ProcessingTimeoutSeconds)*time.Second,
	)
	if err != nil {
		return fmt.Errorf("failed to create partitioned queue consumerGroup: %w", err)
	}

	p.logger.Info("Starting partitioned queue consumerGroup...")

	// Start consuming
	if err := consumerGroup.Start(); err != nil {
		return fmt.Errorf("failed to start partitioned queue consumerGroup: %w", err)
	}

	// Store the consumerGroup
	p.partitionedConsumersGroups[queueName] = consumerGroup

	p.logger.Info("Started partitioned queue consumerGroup",
		"queue", queueName,
		"partitions", queueCfg.Partitions)

	return nil
}

// startSharedQueueConsumer starts a consumer for a shared queue
func (p *JobProcessor) startSharedQueueConsumer(queueName string, queueCfg *queue_config.Queue) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Check if we already have a consumer for this queue
	if _, exists := p.sharedConsumersGroups[queueName]; exists {
		p.logger.Debug("Consumer already exists for shared queue", "queue", queueName)
		return nil
	}

	p.logger.Info("Creating shared queue consumer")

	// Create the message handler
	handler := p.createMessageHandler(queueName)

	println("queueCfg.ProcessingTimeoutSeconds", queueCfg.ProcessingTimeoutSeconds)
	// Create the consumer
	consumerGroup, err := consumer.NewSharedQueueConsumerGroup(
		p.connection,
		default_connection.MainExchangeName,
		queueName,
		queueCfg.Topics,
		handler,
		queueCfg.ConsumersPerInstance,
		queueCfg.MaxRetries,
		time.Duration(queueCfg.ProcessingTimeoutSeconds)*time.Second,
	)
	if err != nil {
		return fmt.Errorf("failed to create shared queue consumer: %w", err)
	}

	// Start consuming
	if err := consumerGroup.Start(); err != nil {
		return fmt.Errorf("failed to start shared queue consumer: %w", err)
	}

	// Store the consumer
	p.sharedConsumersGroups[queueName] = consumerGroup

	p.logger.Info("Started shared queue consumer",
		"queue", queueName,
		"workers", queueCfg.ConsumersPerInstance)

	return nil
}

// createMessageHandler creates a handler function for processing messages
func (p *JobProcessor) createMessageHandler(queueName string) queue.MessageHandler {
	return func(ctx context.Context, rawMessage []byte) error {
		// Parse the job from the message rawMessage
		var message queue.Message
		err := json.Unmarshal(rawMessage, &message)
		if err != nil {
			p.logger.Error("Failed to parse job from message", "queue", queueName, "error", err)
			return err
		}

		logger := p.logger.With("queue", queueName,
			"topic", message.Topic,
			"hashKey", message.HashKey)

		logger.Debug("Processing job")

		// Process the job using the HTTP worker
		if err := p.httpWorker.ProcessMessage(ctx, queueName, &message, rawMessage); err != nil {
			// Log the error and increment retry count
			logger.Error("Failed to process job", "error", err)
			return err
		}

		logger.Debug("Job processed successfully")

		return nil
	}
}

// StopProcessing stops all consumers
func (p *JobProcessor) StopProcessing() error {
	p.logger.Info("Stopping all consumers")

	// Stop shared consumers
	for name, c := range p.sharedConsumersGroups {
		p.logger.Debug("Stopping shared consumer", "queue", name)
		if err := c.Stop(); err != nil {
			p.logger.Error("Error stopping shared consumer", "queue", name, "error", err)
		}
	}

	// Stop partitioned consumers
	for name, c := range p.partitionedConsumersGroups {
		p.logger.Debug("Stopping partitioned consumer", "queue", name)
		if err := c.Stop(); err != nil {
			p.logger.Error("Error stopping partitioned consumer", "queue", name, "error", err)
		}
	}

	p.logger.Info("All consumers stopped")
	return nil
}

// Close releases all resources
func (p *JobProcessor) Close() error {
	p.logger.Info("Closing job processor")

	// Stop all consumers
	if err := p.StopProcessing(); err != nil {
		p.logger.Error("Error stopping processing", "error", err)
	}

	// Close the HTTP worker
	if err := p.httpWorker.Close(); err != nil {
		p.logger.Error("Error closing HTTP worker", "error", err)
	}

	return nil
}
