# -------------------------------------------------
# GitLab CI/CD – single‑pipeline flow on **any** branch
#  * semantic‑release still publishes **only** when the commit is suitable
#    (generally on the default branch), but it now runs everywhere so the
#    pipeline exists for every push.
#  * Build jobs still self‑skip when VERSION is empty.
# -------------------------------------------------

stages:
  - test
  - version
  - build

variables:
  DOCKER_DRIVER: overlay2
  GIT_STRATEGY: clone   # instead of the default 'fetch'

workflow:
  rules:
    - if: '$CI_COMMIT_TAG'   # Skip the entire pipeline for tags
      when: never
    - when: always          # Run for everything else

# -------------------------------------------------
# Reusable templates
# -------------------------------------------------
.go-setup:
  image: golang:1.23
  before_script:
    - cd src

.docker-setup:
  # Use a Docker image with buildx support
  image: docker:23.0.6
  services:
    - docker:23.0.6-dind
  variables:
    DOCKER_BUILDKIT: 1
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login "$CI_REGISTRY" --username "$CI_REGISTRY_USER" --password-stdin
    # Set up Docker Buildx for multi-architecture builds
    - docker buildx create --use --name multiarch-builder
    - docker buildx inspect --bootstrap

# -------------------------------------------------
# 1. Tests –run on **every** pipeline
# -------------------------------------------------
unit-tests:
  extends: .go-setup
  stage: test
  script:
    - go test ./common/... ./consumer/... ./producer/...
  # No rules → always runs (for tags, branches, MRs)

lint:
  extends: .go-setup
  stage: test
  script:
    - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
    - golangci-lint run ./common/... ./consumer/... ./producer/... ./test/... || true
  allow_failure: true
  # No rules → always runs

# -------------------------------------------------
# 2. Version calculation – now runs on *all* branches (but not in tag pipeline)
# -------------------------------------------------
semantic-release:
  stage: version
  image: griefed/gitlab-semantic-release
  before_script:
    - git config --global user.email "<EMAIL>"
    - git config --global user.name  "CI Semantic Release"
  script:
    - |
      set -eo pipefail
      # Run semantic‑release; even if it publishes we stay in this pipeline.
      semantic-release
      echo "semantic-release finished with exit code $?"
      # If a tag **points at HEAD** we just published ➜ expose it via dotenv
      VERSION=$(git tag --points-at HEAD --sort=-v:refname | head -n1 | cut -c 2-)
      if [ -n "$VERSION" ]; then
        echo "New release detected: $VERSION"
        echo "VERSION=$VERSION" >> build.env
      else
        echo "No new release in this run."
      fi
  artifacts:
    reports:
      dotenv: build.env
  rules:
    - if: '$CI_COMMIT_TAG'   # skip tag pipelines (nothing to do)
      when: never
    - when: on_success       # run on every other pipeline

# -------------------------------------------------
# 3. Build images – always scheduled, self‑skip if VERSION is empty
# -------------------------------------------------
.build-base: &build-base
  extends: .docker-setup
  stage: build
  needs: [semantic-release]

build-producer:
  <<: *build-base
  script:
    - |
      if [ -z "$VERSION" ]; then
        echo "VERSION not set ➜ no new release ➜ skipping producer image.";
        exit 0;
      fi
    - echo "Building producer $VERSION for AMD64 and ARM64 architectures"
    - |
      # Build with more verbose output
      docker buildx build --platform linux/amd64,linux/arm64 \
        -t "${PRODUCER_REPOSITORY_URL}:$VERSION" \
        -t "${PRODUCER_REPOSITORY_URL}:latest" \
        -f build/docker/producer/Dockerfile \
        --progress=plain \
        --push .
    - echo "Verifying pushed image:"
    - docker buildx imagetools inspect "${PRODUCER_REPOSITORY_URL}:$VERSION"

build-consumer:
  <<: *build-base
  script:
    - |
      if [ -z "$VERSION" ]; then
        echo "VERSION not set ➜ no new release ➜ skipping consumer image.";
        exit 0;
      fi
    - echo "Building consumer $VERSION for AMD64 and ARM64 architectures"
    - |
      # Build with more verbose output
      docker buildx build --platform linux/amd64,linux/arm64 \
        -t "${CONSUMER_REPOSITORY_URL}:$VERSION" \
        -t "${CONSUMER_REPOSITORY_URL}:latest" \
        -f build/docker/consumer/Dockerfile \
        --progress=plain \
        --push .
    - echo "Verifying pushed image:"
    - docker buildx imagetools inspect "${CONSUMER_REPOSITORY_URL}:$VERSION"